<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打地鼠游戏</title>
    <style>
        body {
            font-family: <PERSON>l, sans-serif;
            text-align: center;
            background: #2c3e50;
            color: white;
            margin: 0;
            padding: 20px;
        }
        
        .game-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 0 10px;
        }
        
        .score-board {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            font-size: 18px;
            flex-wrap: wrap;
        }
        
        /* 添加媒体查询 */
        @media (max-width: 600px) {
            .game-board {
                grid-template-columns: repeat(2, 1fr) !important;
            }
            
            .hole {
                width: 120px !important;
                height: 120px !important;
            }
            
            .mole {
                width: 80px !important;
                height: 80px !important;
            }
            
            .mole::before {
                font-size: 40px !important;
            }
            
            button {
                padding: 10px 20px !important;
                font-size: 16px !important;
                margin: 5px !important;
            }
        }
        
        @media (max-width: 400px) {
            .game-board {
                grid-template-columns: repeat(2, 1fr) !important;
            }
            
            .hole {
                width: 100px !important;
                height: 100px !important;
            }
            
            .mole {
                width: 70px !important;
                height: 70px !important;
            }
            
            .mole::before {
                font-size: 35px !important;
            }
        }
        
        .game-board {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        
        .hole {
            width: 150px;
            height: 150px;
            background: #8b4513;
            border-radius: 50%;
            position: relative;
            cursor: pointer;
            border: 5px solid #654321;
        }
        
        .mole {
            width: 100px;
            height: 100px;
            background: #8B4513;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
            cursor: pointer;
        }
        
        .mole::before {
            content: '🐹';
            font-size: 60px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .mole.show {
            display: block;
            animation: popup 0.3s ease-out;
        }
        
        @keyframes popup {
            0% { transform: translate(-50%, -50%) scale(0); }
            100% { transform: translate(-50%, -50%) scale(1); }
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
        }
        
        button:hover {
            background: #c0392b;
        }
        
        button:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🔨 打地鼠游戏</h1>
        
        <div class="score-board">
            <div>得分: <span id="score">0</span></div>
            <div>时间: <span id="time">30</span>s</div>
            <div>最高分: <span id="highScore">0</span></div>
        </div>
        
        <div class="game-board" id="gameBoard">
            <!-- 9个洞 -->
        </div>
        
        <div class="controls">
            <button id="startBtn">开始游戏</button>
            <button id="stopBtn" disabled>停止游戏</button>
            <button id="resetBtn">重置分数</button>
        </div>
    </div>

    <script>
        class WhackAMole {
            constructor() {
                this.score = 0;
                this.timeLeft = 30;
                this.isPlaying = false;
                this.moleTimer = null;
                this.gameTimer = null;
                this.highScore = localStorage.getItem('whackMoleHighScore') || 0;
                
                this.init();
            }
            
            init() {
                this.createHoles();
                this.updateDisplay();
                this.bindEvents();
            }
            
            createHoles() {
                const gameBoard = document.getElementById('gameBoard');
                for (let i = 0; i < 9; i++) {
                    const hole = document.createElement('div');
                    hole.className = 'hole';
                    hole.innerHTML = `<div class="mole" data-hole="${i}"></div>`;
                    gameBoard.appendChild(hole);
                }
            }
            
            bindEvents() {
                document.getElementById('startBtn').addEventListener('click', () => this.startGame());
                document.getElementById('stopBtn').addEventListener('click', () => this.stopGame());
                document.getElementById('resetBtn').addEventListener('click', () => this.resetHighScore());
                
                document.querySelectorAll('.mole').forEach(mole => {
                    mole.addEventListener('click', (e) => this.whackMole(e));
                });
            }
            
            startGame() {
                this.isPlaying = true;
                this.score = 0;
                this.timeLeft = 30;
                this.updateDisplay();
                
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                
                this.gameTimer = setInterval(() => {
                    this.timeLeft--;
                    this.updateDisplay();
                    
                    if (this.timeLeft <= 0) {
                        this.endGame();
                    }
                }, 1000);
                
                this.showMole();
            }
            
            showMole() {
                if (!this.isPlaying) return;
                
                // 隐藏所有地鼠
                document.querySelectorAll('.mole').forEach(mole => {
                    mole.classList.remove('show');
                });
                
                // 随机显示地鼠
                const randomHole = Math.floor(Math.random() * 9);
                const mole = document.querySelector(`[data-hole="${randomHole}"]`);
                mole.classList.add('show');
                
                // 地鼠显示时间
                const showTime = Math.random() * 1000 + 500; // 0.5-1.5秒
                
                setTimeout(() => {
                    mole.classList.remove('show');
                }, showTime);
                
                // 下一只地鼠出现时间
                const nextMoleTime = Math.random() * 1000 + 600; // 0.6-1.6秒
                this.moleTimer = setTimeout(() => this.showMole(), nextMoleTime);
            }
            
            whackMole(e) {
                if (!this.isPlaying) return;
                
                const mole = e.target;
                if (mole.classList.contains('show')) {
                    this.score += 10;
                    mole.classList.remove('show');
                    this.updateDisplay();
                    
                    // 击中效果
                    mole.style.transform = 'translate(-50%, -50%) scale(1.2)';
                    setTimeout(() => {
                        mole.style.transform = 'translate(-50%, -50%) scale(1)';
                    }, 100);
                }
            }
            
            endGame() {
                this.isPlaying = false;
                clearInterval(this.gameTimer);
                clearTimeout(this.moleTimer);
                
                // 隐藏所有地鼠
                document.querySelectorAll('.mole').forEach(mole => {
                    mole.classList.remove('show');
                });
                
                // 更新最高分
                if (this.score > this.highScore) {
                    this.highScore = this.score;
                    localStorage.setItem('whackMoleHighScore', this.highScore);
                    alert(`🎉 新纪录！得分: ${this.score}`);
                } else {
                    alert(`游戏结束！得分: ${this.score}`);
                }
                
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                this.updateDisplay();
            }
            
            stopGame() {
                // 停止游戏与结束游戏使用相同的逻辑
                this.endGame();
            }
            
            resetHighScore() {
                this.highScore = 0;
                localStorage.removeItem('whackMoleHighScore');
                this.updateDisplay();
            }
            
            updateDisplay() {
                document.getElementById('score').textContent = this.score;
                document.getElementById('time').textContent = this.timeLeft;
                document.getElementById('highScore').textContent = this.highScore;
            }
        }
        
        // 启动游戏
        new WhackAMole();
    </script>
</body>
</html>
